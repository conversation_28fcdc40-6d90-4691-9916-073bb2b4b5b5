import React from "react";
// styled components
import { SettingsHeader, StyledForm } from "@widgets/UserSettings/style";
import Field from "@ui/Field";

// components
import DropFiles from "@components/DropFiles";
import CustomSelect from "@ui/Select";
import { Button, CircularProgress, Dialog, Grid, IconButton } from "@mui/material";
import DateInput from "@components/MaskedInputs/Date";
import { Close, VisibilityOff, Visibility } from "@mui/icons-material";

// hooks
import Page from "@layout/Page";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Box, FormControl, Typography } from "@mui/material";
import styled from "styled-components";
import { textSizes } from "@styles/vars";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "config/firebase.config";
import { CAREGIVER_TYPE_OPTIONS, COLLECTIONS, COUNTRY_CODE } from "@constants/app";
import { addNewUserAction, updateUserAction } from "@store/slices/users";
import { useSnackbar } from "notistack";
import Widget from "@components/Widget";
import { Tab } from "react-bootstrap";
import TabNav from "@ui/TabNav";
import TabNavItem from "@ui/TabNav/TabNavItem";
import { Divider } from "@components/Widget/style";
import WidgetBody from "@components/Widget/WidgetBody";
import { useState } from "react";
import { convertToDataURL, generateNames, generateRandomString } from "@utils/helpers";
import { deleteFileFromStorage, uploadFileToFirebase, uploadImageToFirebase } from "@utils/storage";
import { emergencyPhoneSchema } from "@utils/phoneValidation";
import { firebaseService } from "service/firebase.service";
import AddressInput from "@ui/AddressInput";
import moment from "moment";
import Phone from "@components/MaskedInputs/Phone";
import PhoneNumberInput from "@ui/PhoneNumberInput";

const MAX_FILE_SIZE = 5000000;
const MAX_DATE_DOB = new Date();
MAX_DATE_DOB.setFullYear(new Date().getFullYear() - 5);

const userSchema = z
  .object({
    photo: z.string().optional(),
    role: z.string().nonempty("Role is required"),
    name: z.string().nonempty("Name is required").min(3, "Must have 3 letters atleast"),
    email: z.string().email("Must be a valid email").nonempty("Email is required"),
    password: z.string().optional(),
    gender: z.string().nonempty("Gender is required"),
    dob: z.string().nonempty("Date of birth is required"),
    address: z
      .object({
        formattedAddress: z.string().nonempty("Address is required"),
        placeId: z.string().optional(),
        streetNumber: z.string().optional(),
        streetName: z.string().optional(),
        addressLine2: z.string().optional(),
        city: z.string().optional(),
        state: z.string().optional(),
        postalCode: z.string().optional(),
        country: z.string().optional(),
        countryCode: z.string().optional(),
        lat: z.number().optional(),
        lng: z.number().optional(),
      })
      .optional(),
    type: z.string().optional(),
    emergencyContactPerson: z.object({
      name: z.string().nonempty("Contact Person Name is required"),
      phone: emergencyPhoneSchema,
    }),
    ID_front: z.string().optional(),
    ID_back: z.string().optional(),
    license: z.any().optional(),
    healthCertificate: z.any().optional(),
    certificates: z.array(z.any()).min(1, "Select one file atleast").default([]),
    mode: z.string().nonempty(),
  })
  .superRefine((data, ctx) => {
    // If mode is "add", these fields are required
    if (data.mode === "add") {
      if (!data.password) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Password is required",
          path: ["password"],
        });
      }
      if (!data.ID_front) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "ID Front-side is required",
          path: ["ID_front"],
        });
      }
      if (!data.ID_back) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "ID Back-side is required",
          path: ["ID_back"],
        });
      }
      if (!data.license) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "License is required",
          path: ["license"],
        });
      }
      if (!data.healthCertificate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Health certificate is required",
          path: ["healthCertificate"],
        });
      }
    }

    // If license is provided, validate its size
    if (data.license && data.license.size > MAX_FILE_SIZE) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Max file size is 5MB.",
        path: ["license"],
      });
    }
  })
  .refine(
    (data) => {
      if (data.role === "CAREGIVER") {
        return !!data.type;
      }
      return true;
    },
    {
      message: "Type must be selected",
      path: ["type"],
    },
  );

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const PhotoPreview = styled.img`
  height: 95%;
  width: 95%;
  display: block;
  border-radius: 999px;
`;

const IDPreview = styled.button`
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid lightgrey;
  img {
    height: 60px;
    width: 100px;
    display: block;
    border-radius: 8px;
  }
`;

const StyledField = styled(Field)`
  width: 100%;
`;

const DocPreview = styled.div`
  position: relative;
  margin-top: 8px;
  border-radius: 8px;
  border: 1px solid lightgrey;
  img {
    height: 60px;
    width: 100px;
    display: block;
    border-radius: 8px;
  }
  button {
    position: absolute;
    display: inline-block;
    top: 0;
    right: 0;
    z-index: 10;
    background-color: rgba(75, 75, 75, 0.39);
    height: 24px;
    width: 24px;
    border-radius: 99px;
  }
`;

const GENDER_SELECT = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
];

const RANDOM_PASSWORD = `insyt@${generateRandomString(4)}`;

const AddCaregiver = () => {
  const [activeTab, setActiveTab] = useState("NURSE");
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const [showPassword, setShowPassword] = useState(false);
  const [isPreviewOpen, setPrevewOpen] = useState(false);
  const [previewData, setPrevewData] = useState({ title: "", url: "" });

  const [searchParams] = useSearchParams();
  const user_id = searchParams.get("user_id");


  const dispatch = useDispatch();
  const { nurses, caregivers } = useSelector((state) => state.users);
  const all_users = [...nurses, ...caregivers];
  const current_user = all_users.find((item) => item?.id === user_id);

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
    setError,
    watch,
  } = useForm({
    defaultValues: {
      photo: "",
      role: "",
      email: "",
      password: RANDOM_PASSWORD,
      name: "",
      gender: "",
      dob: "",
      address: {
        formattedAddress: "",
        streetNumber: "",
        streetName: "",
        addressLine2: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
        countryCode: "",
        placeId: "",
        lat: 0,
        lng: 0,
      },
      nurse: "",
      ID_back: "",
      ID_front: "",
      license: "",
      healthCertificate: "",
      mode: "add",
      emergencyContactPerson: { name: "", phone: "" },
      certificates: [],
    },
    resolver: zodResolver(userSchema),
    mode: "all",
  });

  async function onCopyCredentials() {
    const name = watch("name");
    const email = watch("email");
    const password = watch("password");
    const credentialsText = `Name: ${name}\nEmail: ${email}\nPassword: ${password}`;
    try {
      await navigator.clipboard.writeText(credentialsText);
      enqueueSnackbar("Credentials copied to clipboard", { variant: "success" });
    } catch (error) {
      enqueueSnackbar("Failed to copy credentials", { variant: "error" });
    }
  }

  function isCopyBtnDisabled() {
    const name = watch("name");
    const email = watch("email");
    const password = watch("password");
    const hasErrors = errors.name || errors.email || errors.password;
    const hasEmptyFields = !name || !email || !password;
    return hasErrors || hasEmptyFields;
  }

  async function onSelectFile(files, key) {
    const file = files && files[0] ? files[0] : null;
    if (file) {
      const data_url_image = await convertToDataURL(file);
      setValue(key, data_url_image);
      setError(key, { message: undefined });
    }
  }

  async function submitForm(formValues) {
    const { name, gender, email, password, dob, address, type, emergencyContactPerson } = formValues;

    // * CREATE NEW USER
    if (!user_id) {
      const [ID_front, ID_back, license, healthCertificate] = await Promise.all([
        uploadImageToFirebase("IDsFront", formValues?.ID_front),
        uploadImageToFirebase("IDsBack", formValues?.ID_back),
        uploadFileToFirebase("Licenses", formValues?.license, "pdf"),
        uploadFileToFirebase("HealthCertificates", formValues?.healthCertificate, "pdf"),
      ]);
      let photo = { url: "", path: "" };
      if (formValues?.photo) {
        photo = await uploadImageToFirebase("Profiles", formValues?.photo);
      }

      let certificates = [];
      if (formValues?.certificates?.length > 0) {
        const certificates_promises = formValues?.certificates?.map((item) =>
          uploadFileToFirebase("Certificates", item, "pdf"),
        );
        certificates = await Promise.all(certificates_promises);
      }

      // ROLE: NURSE
      if (formValues?.role === "NURSE") {
        try {
          const payload = {
            email: email?.toLowerCase(),
            password,
            name,
            gender,
            dob,
            address,
            emergencyContactPerson: {
              name: emergencyContactPerson?.name,
              phone: `${COUNTRY_CODE} ${emergencyContactPerson?.phone}`,
            },
            photo,
            ID: {
              front: ID_front,
              back: ID_back,
            },
            license,
            healthCertificate,
            certificates,
            role: "NURSE",
            isActive: true,
          };

          await firebaseService
            .createUser(payload)
            .then(({ data }) => {
              enqueueSnackbar("New nurse added", { variant: "success" });
              const transformedUser = {
                ...data.user,
                firstName: generateNames(data?.user?.name)?.firstName,
                lastName: generateNames(data?.user?.name)?.lastName,
                department: [{ id: "nurse", value: "nurse", label: "Registered Nurse" }],
              };
              dispatch(addNewUserAction(transformedUser));
              navigate("/staff");
            })
            .catch((error) => {
              [photo, ID_front, ID_back, license].map(({ url }) => deleteFileFromStorage(url));
              if (error?.response?.data?.code === "auth/email-already-exists") {
                enqueueSnackbar("Email is already taken", { variant: "error" });
              }
            });
        } catch (error) {
          enqueueSnackbar("Couldn't add new nurse", { variant: "error" });
        }
      }

      // ROLE: CAREGIVER
      if (formValues?.role === "CAREGIVER") {
        try {
          const payload = {
            email,
            password,
            name,
            gender,
            dob,
            address,
            type,
            emergencyContactPerson: {
              name: emergencyContactPerson?.name,
              phone: `${COUNTRY_CODE} ${emergencyContactPerson?.phone}`,
            },
            isActive: true,
            photo,
            role: "CAREGIVER",
            ID: {
              front: ID_front,
              back: ID_back,
            },
            license,
            nurse: formValues?.nurse,
            healthCertificate,
            certificates,
          };

          await firebaseService
            .createUser(payload)
            .then(({ data }) => {
              enqueueSnackbar("New caregiver added", { variant: "success" });
              const transformedUser = {
                ...data.user,
                firstName: data.user.name.split(" ")[0],
                lastName: data.user.name.split(" ")[1],
                department: [{ id: "caregiver", value: "caregiver", label: "Caregiver" }],
              };
              dispatch(addNewUserAction(transformedUser));
              navigate("/staff");
            })
            .catch((error) => {
              [photo, ID_front, ID_back, license].map(({ url }) => deleteFileFromStorage(url));
              if (error?.response?.data?.code === "auth/email-already-exists") {
                enqueueSnackbar("Email is already taken", { variant: "error" });
              }
            });
        } catch (error) {
          enqueueSnackbar("Couldn't add new caregiver", { variant: "error" });
        }
      }
    }

    // * UDPATE NURSE
    if (user_id && current_user) {
      try {
        let payload = {
          ...current_user,
          name,
          gender,
          dob,
          address,
          ...(current_user.role === "CAREGIVER" && { type }),
          emergencyContactPerson: {
            name: emergencyContactPerson?.name,
            phone: `${COUNTRY_CODE} ${emergencyContactPerson?.phone}`,
          },
        };

        // PROFILE PICTURE
        if (formValues?.photo !== current_user?.photo?.url) {
          const path_for_profile = current_user?.photo?.path?.split("/")[1]
            ? current_user?.photo?.path?.split("/")[1]
            : undefined;

          payload.photo = await uploadImageToFirebase("Profiles", formValues?.photo, path_for_profile);
        }

        // ID: FRONT SIDE
        if (formValues?.ID_front !== current_user?.ID?.front?.url) {
          await uploadImageToFirebase("IDsFront", formValues?.ID_front, current_user?.ID?.front?.path?.split("/")[1]);
        }

        // ID: BACK SIDE
        if (formValues?.ID_back !== current_user?.ID?.back?.url) {
          await uploadImageToFirebase("IDsBack", formValues?.ID_back, current_user?.ID?.back?.path?.split("/")[1]);
        }

        // LICENSE
        if (formValues?.license !== current_user?.license?.url) {
          await uploadFileToFirebase(
            "Licenses",
            formValues?.license,
            undefined,
            current_user?.license?.path?.split("/")[1],
          );
        }
        // CERTIFICATES: NEW FILES & YET TO UPLOAD
        const newCertificates = formValues?.certificates?.filter(
          (cert) => !current_user?.certificates?.some((existingCert) => existingCert.url === cert),
        );

        // FIND REMOVED CERTIFICATES
        const removedCertificates = current_user?.certificates?.filter(
          (existingCert) => !formValues?.certificates?.some((cert) => cert === existingCert.url),
        );

        // UPDAD NEW CERTIFICATES
        let certificates = [];
        if (newCertificates?.length > 0) {
          const certificates_promises = newCertificates?.map((item) =>
            uploadFileToFirebase("Certificates", item, "pdf"),
          );
          const newlyUploadedCertificates = await Promise.all(certificates_promises);

          // COMBINE ARRAY OF CERTIFICATES
          certificates = [
            ...(current_user?.certificates?.filter((cert) => !removedCertificates?.includes(cert)) || []),
            ...newlyUploadedCertificates,
          ];

          payload.certificates = certificates;
        }

        // DELETE THE REMOVED CERTIFICATES
        if (removedCertificates?.length > 0) {
          removedCertificates.forEach((cert) => {
            if (cert?.url) {
              deleteFileFromStorage(cert.url);
            }
          });
        }

        await updateDoc(doc(db, COLLECTIONS.USERS, user_id), payload)
          .then(() => {
            enqueueSnackbar("User updated successfully", { variant: "success" });
            const transformedUser = {
              ...payload,
              firstName: generateNames(payload?.name)?.firstName,
              lastName: generateNames(payload?.name)?.lastName,
            };
            dispatch(updateUserAction(transformedUser));
            navigate("/staff");
          })
          .catch((error) => {
            console.log("updatDoc >>", error);
            enqueueSnackbar("Coudn't update the user", { variant: "error" });
          });
      } catch (error) {
        console.log("ERROR UPDATE USER >>", error);
        enqueueSnackbar("Error occured while updating user", { variant: "error" });
      }
    }
  }

  useEffect(() => {
    if (user_id && current_user) {
      setActiveTab(current_user?.role);
      setValue("photo", current_user?.photo?.url || "");
      setValue("name", current_user?.name);
      setValue("email", current_user?.email);
      setValue("gender", current_user?.gender);
      setValue("dob", moment(current_user?.dob).format("YYYY-MM-DD"));
      setValue("address", {
        formattedAddress: current_user?.address?.formattedAddress || "",
        streetNumber: current_user?.address?.streetNumber || "",
        streetName: current_user?.address?.streetName || "",
        addressLine2: current_user?.address?.addressLine2 || "",
        city: current_user?.address?.city || "",
        state: current_user?.address?.state || "",
        postalCode: current_user?.address?.postalCode || "",
        country: current_user?.address?.country || "",
        countryCode: current_user?.address?.countryCode || "",
        placeId: current_user?.address?.placeId || "",
        lat: current_user?.address?.lat || 0,
        lng: current_user?.address?.lng || 0,
      });
      setValue("type", current_user?.type || "");
      setValue("emergencyContactPerson.name", current_user?.emergencyContactPerson?.name || "");
      setValue("emergencyContactPerson.phone", current_user?.emergencyContactPerson?.phone || "");
      setValue("ID_front", current_user?.ID?.front?.url || "");
      setValue("ID_back", current_user?.ID?.back?.url || "");
      setValue("license", current_user?.license?.url || "");
      setValue("healthCertificate", current_user?.healthCertificate?.url || "");
      setValue("certificates", current_user?.certificates?.map((item) => item?.url) || []);
      setValue("mode", "edit");
    }
  }, [all_users?.length]);

  useEffect(() => {
    setValue("role", activeTab);
    if (current_user?.role === "CAREGIVER") {
      setValue("type", current_user?.type);
    } else {
      setValue("type", undefined);
    }
  }, [activeTab]);



  return (
    <>
      <Page title={user_id ? "Edit Staff" : "Add Staff"}>
        <Widget name="UserSettings">
          <Tab.Container defaultActiveKey="NURSE" transition={true} activeKey={activeTab}>
            <SettingsHeader>
              <div className="wrapper">
                <h2 className="title">{activeTab?.toLowerCase()}</h2>
                {!user_id ? (
                  <TabNav>
                    <TabNavItem eventKey="NURSE" title="Nurse" handler={() => setActiveTab("NURSE")} />
                    <TabNavItem eventKey="CAREGIVER" title="Caregiver" handler={() => setActiveTab("CAREGIVER")} />
                  </TabNav>
                ) : null}
              </div>
              <Divider />
            </SettingsHeader>
            <WidgetBody>
              <Tab.Content>
                <Tab.Pane active>
                  {/* FORM */}
                  <StyledForm onSubmit={handleSubmit(submitForm)}>
                    <div className="wrapper">
                      <Box>
                        <DropFiles
                          multiple={false}
                          type="image"
                          onDrop={(val) => {
                            onSelectFile(val, "photo");
                          }}
                        >
                          {watch("photo") ? (
                            <PhotoPreview src={watch("photo")} />
                          ) : (
                            <>
                              <i className="icon icon-image" />
                              <span className="hint">{"Select image"}</span>
                            </>
                          )}
                        </DropFiles>
                        {errors?.photo?.message && (
                          <Typography color="error" variant="body1" fontSize={"12px"} textAlign="center">
                            {errors?.photo?.message}
                          </Typography>
                        )}
                      </Box>
                      <Grid container spacing={2}>
                        {/* EMAIL */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Controller
                            name="email"
                            control={control}
                            render={({ field }) => {
                              return (
                                <>
                                  <Label htmlFor="email">Email</Label>
                                  <StyledField
                                    type="text"
                                    id="email"
                                    {...field}
                                    disabled={!!user_id}
                                    placeholder="Email"
                                  />
                                </>
                              );
                            }}
                          />
                          {errors?.email?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.email?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* PASSWORD */}
                        {!current_user ? (
                          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                            <Controller
                              name="password"
                              control={control}
                              render={({ field }) => {
                                return (
                                  <>
                                    <Label htmlFor="password">Password</Label>
                                    <Box position="relative" height="fit-content">
                                      <StyledField
                                        type={showPassword ? "text" : "password"}
                                        id="password"
                                        {...field}
                                        disabled={!!user_id}
                                        placeholder="Password"
                                      />
                                      <IconButton
                                        onClick={() => setShowPassword(!showPassword)}
                                        sx={{ position: "absolute", top: 0, bottom: 0, right: 2 }}
                                      >
                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                      </IconButton>
                                    </Box>
                                  </>
                                );
                              }}
                            />
                            {errors?.password?.message && (
                              <Typography color="error" variant="caption">
                                {errors?.password?.message}
                              </Typography>
                            )}
                          </Grid>
                        ) : (
                          <Grid size={{ xs: 12, sm: 6 }}></Grid>
                        )}

                        {/* NAME */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Controller
                            name="name"
                            control={control}
                            render={({ field }) => {
                              return (
                                <>
                                  <Label htmlFor="name">Name</Label>
                                  <StyledField type="text" id="name" {...field} placeholder="Name" />
                                </>
                              );
                            }}
                          />
                          {errors?.name?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.name?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* GENDER */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Controller
                            name="gender"
                            control={control}
                            render={({ field }) => (
                              <>
                                <Label htmlFor="gender">Gender</Label>
                                <CustomSelect
                                  options={GENDER_SELECT}
                                  variant="basic"
                                  value={GENDER_SELECT.find((opt) => opt.value === field.value) || null}
                                  changeHandler={(selected) => field.onChange(selected?.value)}
                                  placeholder="Select Gender"
                                />
                                {errors?.gender?.message && (
                                  <Typography color="error" variant="caption">
                                    {errors?.gender?.message}
                                  </Typography>
                                )}
                              </>
                            )}
                          />
                        </Grid>

                        {/* DOB */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Controller
                            name="dob"
                            control={control}
                            render={({}) => (
                              <>
                                <Label htmlFor="dob">Date of Birth</Label>
                                <DateInput
                                  id="dob"
                                  maxDate={MAX_DATE_DOB}
                                  onChange={(val) => {
                                    const formatted_val = moment(val).format("YYYY-MM-DD");
                                    setValue("dob", formatted_val);
                                    setError("dob", { message: undefined });
                                  }}
                                  value={watch("dob")}
                                />
                                {errors?.dob?.message && (
                                  <Typography color="error" variant="caption">
                                    {errors?.dob?.message}
                                  </Typography>
                                )}
                              </>
                            )}
                          />
                        </Grid>

                        {/* ADDRESS */}
                        <Grid size={{ xs: 12 }} position="relative" height="fit-content">
                          <Controller
                            name="address"
                            control={control}
                            render={({ field }) => (
                              <AddressInput
                                value={field.value}
                                onChange={field.onChange}
                                error={errors?.address}
                                apiKey={process.env.REACT_APP_GOOGLE_MAP_KEY}
                                required
                                placeholder="Start typing your address..."
                              />
                            )}
                          />
                        </Grid>

                        {/* TYPE*/}
                        {watch("role") === "CAREGIVER" ? (
                          <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                            <Controller
                              name="type"
                              control={control}
                              render={({ field }) => (
                                <>
                                  <Label htmlFor="type">Type</Label>
                                  <CustomSelect
                                    options={CAREGIVER_TYPE_OPTIONS}
                                    key={watch("type")}
                                    variant="basic"
                                    value={CAREGIVER_TYPE_OPTIONS.find((opt) => opt.value === field.value) || null}
                                    changeHandler={(selected) => field.onChange(selected?.value)}
                                    placeholder="Select Type"
                                  />
                                  {errors?.type?.message && (
                                    <Typography color="error" variant="caption">
                                      {errors?.type?.message}
                                    </Typography>
                                  )}
                                </>
                              )}
                            />
                          </Grid>
                        ) : null}

                        <Grid size={12}>
                          <Divider sx={{ marginTop: 2 }} />
                        </Grid>

                        {/* EMERGENCY CONTACT PERSON */}
                        <Grid size={12}>
                          <Typography variant="h6" gridColumn="1 / span 2">
                            Emergency Contact Person
                          </Typography>
                        </Grid>

                        {/* NAME */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Controller
                            name="emergencyContactPerson.name"
                            control={control}
                            render={({ field }) => {
                              return (
                                <>
                                  <Label htmlFor="contactPersonName">Contact Person Name</Label>
                                  <StyledField
                                    type="text"
                                    id="contactPersonName"
                                    {...field}
                                    placeholder="Contact Person Name"
                                  />
                                </>
                              );
                            }}
                          />
                          {errors?.emergencyContactPerson?.name?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.emergencyContactPerson?.name?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* EMERGENCY CONTACT */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Controller
                            name="emergencyContactPerson.phone"
                            control={control}
                            render={({ field }) => {
                              return (
                                <>
                                  <Label htmlFor="emergencyContactPerson">Contact Person Phone</Label>
                                  <PhoneNumberInput
                                    id="emergencyContactPerson"
                                    value={field.value}
                                    onChange={field.onChange}
                                    placeholder="Enter emergency contact phone"
                                    error={!!errors?.emergencyContactPerson?.phone}
                                  />
                                </>
                              );
                            }}
                          />
                          {errors?.emergencyContactPerson?.phone?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.emergencyContactPerson?.phone?.message}
                            </Typography>
                          )}
                        </Grid>

                        <Grid size={12}>
                          <Divider sx={{ marginTop: 2 }} />
                        </Grid>
                        <Grid size={12}>
                          <Typography variant="h6" gridColumn="1 / span 2">
                            Documents
                          </Typography>
                        </Grid>

                        {/* ID FRONT SIDE */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Label htmlFor="id-front">ID Front Side</Label>
                          <StyledField
                            id="id-front"
                            type="file"
                            accept=".png,.jpg,.jpeg"
                            onChange={(e) => onSelectFile(e?.target?.files, "ID_front")}
                          />
                          {watch("ID_front") && (
                            <IDPreview
                              onClick={() => {
                                setPrevewData({ title: "Preview", url: watch("ID_front") });
                                setPrevewOpen(true);
                              }}
                              type="button"
                            >
                              <img src={watch("ID_front")} />
                            </IDPreview>
                          )}
                          {errors?.ID_front?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.ID_front?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* ID BACK SIDE */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Label htmlFor="id-back">ID Back Side</Label>
                          <StyledField
                            id="id-back"
                            type="file"
                            accept=".png,.jpg,.jpeg"
                            onChange={(e) => onSelectFile(e?.target?.files, "ID_back")}
                          />
                          {watch("ID_back") && (
                            <IDPreview
                              onClick={() => {
                                setPrevewData({ title: "Preview", url: watch("ID_back") });
                                setPrevewOpen(true);
                              }}
                              type="button"
                            >
                              <img src={watch("ID_back")} />
                            </IDPreview>
                          )}
                          {errors?.ID_back?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.ID_back?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* LICENSE/CERTIFICATE */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Label htmlFor="license">License</Label>
                          <StyledField
                            id="license"
                            type="file"
                            accept=".pdf"
                            onChange={(e) => {
                              if (e?.target?.files) {
                                setValue("license", e?.target?.files[0]);
                                setError("license", { message: undefined });
                              }
                            }}
                          />
                          {watch("license") && (
                            <Box mt={1}>
                              <a
                                href={
                                  typeof watch("license") === "string"
                                    ? watch("license")
                                    : URL.createObjectURL(watch("license"))
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: "#1976d2", textDecoration: "none" }}
                              >
                                View PDF File
                              </a>
                            </Box>
                          )}
                          {errors?.license?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.license?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* HEALTH CERTIFICATE */}
                        <Grid size={{ xs: 12, sm: 6 }} position="relative" height="fit-content">
                          <Label htmlFor="healthCertificate">Health Certificate</Label>
                          <StyledField
                            id="healthCertificate"
                            type="file"
                            accept=".pdf"
                            onChange={(e) => {
                              if (e?.target?.files) {
                                setValue("healthCertificate", e?.target?.files[0]);
                                setError("healthCertificate", { message: undefined });
                              }
                            }}
                          />
                          {watch("healthCertificate") && (
                            <Box mt={1}>
                              <a
                                href={
                                  typeof watch("healthCertificate") === "string"
                                    ? watch("healthCertificate")
                                    : URL.createObjectURL(watch("healthCertificate"))
                                }
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: "#1976d2", textDecoration: "none" }}
                              >
                                View PDF File
                              </a>
                            </Box>
                          )}
                          {errors?.healthCertificate?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.healthCertificate?.message}
                            </Typography>
                          )}
                        </Grid>

                        {/* HEALTH CERTIFICATE */}
                        <Grid size={{ xs: 12 }} position="relative" height="fit-content">
                          <Label htmlFor="certificates">Certificates</Label>
                          <StyledField
                            id="certificates"
                            type="file"
                            accept=".pdf"
                            onChange={(e) => {
                              const files = Array.from(e.target.files);
                              if (files?.length > 0) {
                                setValue("certificates", [...watch("certificates"), ...files]);
                                setError("certificates", { message: undefined });
                              }
                            }}
                            multiple
                          />

                          {errors?.certificates?.message && (
                            <Typography color="error" variant="caption">
                              {errors?.certificates?.message}
                            </Typography>
                          )}

                          {watch("certificates").length > 0
                            ? watch("certificates").map((item, index) => (
                                <Box mt={1} key={index}>
                                  <a
                                    href={typeof item === "string" ? item : URL.createObjectURL(item)}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{ color: "#1976d2", textDecoration: "none" }}
                                  >
                                    {typeof item === "string" ? "View File" : item?.name}
                                  </a>
                                </Box>
                              ))
                            : null}
                        </Grid>

                        <Grid
                          size={12}
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            marginTop: 2,
                            justifyContent: "end",
                            alignItems: "end",
                            gridColumn: "1/3",
                            gap: 1.5,
                          }}
                        >
                          {!user_id ? (
                            <Button
                              fullWidth
                              variant="contained"
                              color="secondary"
                              type="button"
                              sx={{
                                fontSize: 16,
                                borderRadius: 2,
                                textTransform: "none",
                                fontWeight: 400,
                                ":hover": { color: "#000" },
                                maxWidth: "200px",
                              }}
                              onClick={onCopyCredentials}
                              disabled={isCopyBtnDisabled()}
                            >
                              {"Copy Credentials"}
                            </Button>
                          ) : null}
                          <Button
                            fullWidth
                            variant="contained"
                            color="primary"
                            type="submit"
                            sx={{
                              fontSize: 16,
                              borderRadius: 2,
                              textTransform: "none",
                              fontWeight: 400,
                            }}
                            disabled={isSubmitting}
                          >
                            {"Save"}
                            {isSubmitting ? (
                              <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} />
                            ) : null}
                          </Button>
                        </Grid>
                      </Grid>
                    </div>
                  </StyledForm>
                </Tab.Pane>
              </Tab.Content>
            </WidgetBody>
          </Tab.Container>
        </Widget>

        {/* ID PREIVEW MODAL */}
        <Dialog
          open={isPreviewOpen}
          onClose={() => setPrevewOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          sx={{ zIndex: 300000, minWidth: { xs: "90%", md: "500px" } }}
        >
          <Box width="100%" display="flex" flexDirection="column" justifyContent="center" alignItems="center">
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              py={1}
              px={1}
              flexGrow={1}
              width="100%"
            >
              <Typography variant="h6">{previewData?.title}</Typography>
              <IconButton onClick={() => setPrevewOpen(false)} sx={{ p: 0.5 }}>
                <Close sx={{ height: 16, width: 16 }} />
              </IconButton>
            </Box>
            <img
              src={previewData?.url}
              style={{ minWidth: "200px", width: "90%", objectFit: "contain", maxHeight: "500px" }}
            />
          </Box>
        </Dialog>
      </Page>
    </>
  );
};

export default AddCaregiver;
